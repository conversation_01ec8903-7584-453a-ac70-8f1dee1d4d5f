<h2>Who is this for?</h2>
<p>This template is ideal for <strong>developers, DevOps engineers, and automation managers</strong> who manage their n8n workflows using GitHub. It helps teams streamline their CI/CD automation by syncing changes from GitHub directly into n8n after a pull request (PR) is merged.</p>
<h2>What problem is this workflow solving?</h2>
<p>Manually restoring workflows after reviewing and merging code in GitHub can be tedious and error-prone. This workflow solves that by <strong>automating the restore process</strong>, ensuring that any new or updated workflow committed to your GitHub repo is automatically imported into your n8n environment.</p>
<h2>What this workflow does</h2>
<ul>
<li>Triggers when a GitHub pull request is <strong>closed and merged</strong>.</li>
<li>Fetches the details of the merge commit.</li>
<li>Retrieves the list of <strong>added and modified workflow files</strong>.</li>
<li>Downloads and decodes each workflow file.</li>
<li><strong>Creates or updates</strong> the corresponding workflow in your n8n instance automatically.</li>
</ul>
<h2>Setup</h2>
<ol>
<li><strong>Connect GitHub</strong>: Use the <code>GitHub Trigger</code> node and configure GitHub API credentials.<br>
Note: I'd recommended to use <em>GitHub PAT (Personal Access Token) classic</em> with <code>repo</code> and <code>admin:repo_hook</code> permission scopes enabled.</li>
<li><strong>Connect n8n API</strong>: Provide your n8n API credentials in the <code>n8n</code> nodes. - <a href="https://docs.n8n.io/api/authentication/">Check this doc</a></li>
<li><strong>Set repository variables</strong>: Update <code>github_owner</code> and <code>repo_name</code> in the <strong>Define Local Variables</strong> node.</li>
<li><strong>Enable webhook</strong>: Make sure your GitHub repository has a webhook for <code>pull_request</code> events pointing to this workflow.</li>
</ol>
<h2>How to customize this workflow to your needs</h2>
<ul>
<li>Modify filters to handle only certain branches or file paths.</li>
<li>Add Slack or email notifications to confirm successful imports.</li>
<li>Insert logging or version tagging for better traceability.</li>
<li>Extend with conditional logic for workflow testing before applying changes.</li>
</ul>
<p>This automated flow provides a seamless CI/CD loop between GitHub and n8n, empowering teams to manage workflow versioning efficiently and securely.</p>