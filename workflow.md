<h2>Who is this for?</h2>
<p>This template is ideal for <strong>n8n administrators, automation engineers, and DevOps teams</strong> who want to maintain bidirectional synchronization between their n8n workflows and GitHub repositories. It helps teams keep their workflow backups up-to-date and ensures consistency between their n8n instance and version control system.</p>

<h2>What problem is this workflow solving?</h2>
<p>Managing workflow versions across n8n and GitHub can become complex when changes happen in both places. This workflow solves that by <strong>automatically synchronizing workflows bidirectionally</strong>, ensuring that the most recent version is always available in both systems without manual intervention or version conflicts.</p>

<h2>What this workflow does</h2>
<ul>
<li>Runs on a <strong>weekly schedule</strong> (every Monday) to check for synchronization needs.</li>
<li>Fetches all workflows from your n8n instance and compares them with GitHub repository files.</li>
<li>Identifies workflows that exist <strong>only in n8n</strong> and uploads them to GitHub as JSON backups.</li>
<li>Identifies workflows that exist <strong>only in GitHub</strong> and creates them in your n8n instance.</li>
<li>For workflows that exist in both places, compares timestamps and <strong>syncs the most recent version</strong>:
  <ul>
    <li>If n8n version is newer → Updates GitHub with the latest workflow</li>
    <li>If GitHub version is newer → Updates n8n with the latest workflow</li>
  </ul>
</li>
<li>Automatically handles file naming, encoding/decoding, and commit messages with timestamps.</li>
</ul>

<h2>Setup</h2>
<ol>
<li><strong>Connect GitHub</strong>: Configure GitHub API credentials in the GitHub nodes.<br>
Note: Use a <em>GitHub Personal Access Token (classic)</em> with <code>repo</code> permissions to read and write workflow files.</li>
<li><strong>Connect n8n API</strong>: Provide your n8n API credentials in the <code>n8n</code> nodes. - <a href="https://docs.n8n.io/api/authentication/">Check this doc</a></li>
<li><strong>Configure GitHub Details</strong>: Update the <strong>Set GitHub Details</strong> node with:
  <ul>
    <li><code>github_account_name</code>: Your GitHub username or organization</li>
    <li><code>github_repo_name</code>: The repository name where workflows should be stored</li>
    <li><code>repo_workflows_path</code>: The folder path in your repo (e.g., "workflows" or "n8n-workflows")</li>
  </ul>
</li>
<li><strong>Adjust Schedule</strong>: Modify the <strong>Schedule Trigger</strong> if you want a different sync frequency (currently set to weekly on Mondays).</li>
<li><strong>Test the workflow</strong>: Run it manually first to ensure all connections and permissions are working correctly.</li>
</ol>

<h2>How to customize this workflow to your needs</h2>
<ul>
<li><strong>Change sync frequency</strong>: Modify the Schedule Trigger to run daily, hourly, or on-demand.</li>
<li><strong>Add filtering</strong>: Extend the Filter node to exclude certain workflows (e.g., test workflows, templates).</li>
<li><strong>Add notifications</strong>: Insert Slack, email, or webhook notifications to report sync results.</li>
<li><strong>Implement conflict resolution</strong>: Add custom logic for handling workflows with the same timestamp.</li>
<li><strong>Add workflow validation</strong>: Include checks to validate workflow JSON before syncing.</li>
<li><strong>Branch management</strong>: Modify to sync to different branches or create pull requests instead of direct commits.</li>
<li><strong>Backup retention</strong>: Add logic to maintain multiple versions or archive old workflows.</li>
</ul>

<h2>Key Features</h2>
<ul>
<li><strong>Bidirectional sync</strong>: Handles changes from both n8n and GitHub</li>
<li><strong>Timestamp-based conflict resolution</strong>: Always keeps the most recent version</li>
<li><strong>Automatic file naming</strong>: Converts workflow names to valid filenames</li>
<li><strong>Base64 encoding/decoding</strong>: Properly handles JSON workflow data</li>
<li><strong>Comprehensive comparison</strong>: Uses dataset comparison to identify differences</li>
<li><strong>Automated commits</strong>: Includes timestamps in commit messages for traceability</li>
</ul>

<p>This automated synchronization workflow provides a robust backup and version control solution for n8n workflows, ensuring your automation assets are always safely stored and consistently available across environments.</p>
